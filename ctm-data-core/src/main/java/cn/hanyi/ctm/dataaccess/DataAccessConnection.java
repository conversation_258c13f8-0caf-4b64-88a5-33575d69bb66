package cn.hanyi.ctm.dataaccess;

import cn.hanyi.ctm.dto.DataAccessDataDto;
import cn.hanyi.ctm.entity.DataAccess;
import cn.hanyi.ctm.entity.DataAccessParams;
import lombok.Getter;

import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;

@Getter
public abstract class DataAccessConnection<DATA extends DataAccessDataDto> {

    private final DataAccess dataAccess;
    private final List<DataAccessParams> dataAccessParams;
    private final AtomicBoolean alive = new AtomicBoolean(false);

    public DataAccessConnection(DataAccess dataAccess, List<DataAccessParams> params) {
        this.dataAccess = dataAccess;
        this.dataAccessParams = params;
    }

    protected abstract void init();

    protected void afterInit() {
        alive.set(true);
    }

    public abstract List<DATA> getData(int size) throws InterruptedException;

    public abstract void ack(DATA data);

    public Long id() {
        return dataAccess.getId();
    }

    public int currentVersion() {
        return dataAccess.getVersion();
    }

    /**
     * 连接状态
     */
    public boolean isAlive() {
        return alive.get();
    }

    /**
     * 标记连接失效
     */
    public void destroy() {
        alive.set(false);
    }

    /**
     * 释放资源
     */
    public void release() {

    }
}
