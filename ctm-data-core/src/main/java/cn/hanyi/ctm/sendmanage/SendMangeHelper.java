package cn.hanyi.ctm.sendmanage;

import cn.hanyi.cem.core.constant.CustomerSendFromType;
import cn.hanyi.cem.core.dto.task.TaskCustomerSendCompositedDto;
import cn.hanyi.ctm.constant.*;
import cn.hanyi.ctm.dto.SendChannelConfigDto;
import cn.hanyi.ctm.dto.SurveyLinkDto;
import cn.hanyi.ctm.dto.TemplateInfoDto;
import cn.hanyi.ctm.dto.customer.CustomerClientIdParamDto;
import cn.hanyi.ctm.dto.journey.DisturbMomentDto;
import cn.hanyi.ctm.dto.journey.PushMomentDto;
import cn.hanyi.ctm.entity.*;
import cn.hanyi.ctm.repository.SendManageRecordRepository;
import cn.hanyi.ctm.repository.SendManageRepository;
import cn.hanyi.ctm.service.ConnectorService;
import cn.hanyi.ctm.service.CustomerMessageService;
import cn.hanyi.ctm.service.CustomerService;
import cn.hanyi.ctm.workertrigger.ICtmTaskTrigger;
import cn.hanyi.survey.core.projection.SimpleSurvey;
import cn.hanyi.survey.core.repository.SurveyRepository;
import cn.hutool.core.date.DateUtil;
import java.time.ZoneId;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.RandomUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.befun.auth.service.UserTaskService;
import org.befun.auth.service.auth.config.WechatOpenConfig;
import org.befun.core.template.TemplateEngine;
import org.befun.core.utils.DateHelper;
import org.befun.core.utils.JsonHelper;
import org.befun.extension.entity.Link;
import org.befun.extension.service.LinkService;
import org.befun.task.dto.TimedTaskDto;
import org.befun.task.utils.TimeUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.redis.core.StringRedisTemplate;

import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Supplier;

@SuppressWarnings("unchecked")
@Slf4j
public abstract class SendMangeHelper {

    @Autowired
    private SendManageRepository sendManageRepository;
    @Autowired
    private SendManageRecordRepository sendManageRecordRepository;
    @Autowired
    private SurveyRepository surveyRepository;
    @Autowired
    private LinkService linkService;
    @Autowired
    private ConnectorService connectorService;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    @Autowired
    protected ICtmTaskTrigger ctmTaskTrigger;
    @Autowired
    protected UserTaskService userTaskService;
    @Autowired
    protected CustomerMessageService customerMessageService;
    @Autowired
    protected CustomerService customerService;

    protected SendManage getSendManageById(Long orgId, Long sendManageId) {
        SendManage sendManage = sendManageRepository.findById(sendManageId).orElse(null);
        if (sendManage == null || !sendManage.getOrgId().equals(orgId)) {
            return null;
        }
        return sendManage;
    }

    protected boolean sendManage(Long orgId, Long userId, Long sourceId, SendManage sendManage, Map<String, Object> params,
                                 Supplier<Boolean> checkFilter,
                                 Supplier<Customer> getCustomer) {
        SendManageRecord record = sendManage(orgId, userId, sourceId, null, sendManage, params, checkFilter, getCustomer);
        return record != null && !record.getStatus().isCompleted();
    }

    protected SendManageRecord sendManage(Long orgId, Long userId, Long sourceId, Long taskProgressId, SendManage sendManage, Map<String, Object> params,
                                          Supplier<Boolean> checkFilter,
                                          Supplier<Customer> getCustomer) {
        if (sendManage.getEnable() == null || !sendManage.getEnable()) {
            return null;
        }
        // 检验推送条件
        Boolean filter = checkFilter.get();
        if (!filter) {
            return null;
        }
        // 获得一个随机问卷
        Long surveyId = getRandomSurveyId(sendManage.getSendSids());
        if (surveyId == null || surveyId <= 0) {
            return null;
        }
        SimpleSurvey survey = surveyRepository.findSimpleById(surveyId);
        if (survey == null) {
            return null;
        }
        // 获取客户
        Customer customer = getCustomer.get();
        if (customer == null) {
            return null;
        }
        if (params == null) {
            params = new HashMap<>();
        }
        // 写入推送记录
        SendManageRecord record = buildRecord(sendManage, sourceId, surveyId, customer.getId());
        record.setTaskProgressId(taskProgressId);
        // 校验是否为免打扰
        if (disturb(sendManage.getDisturbMoment(), customer.getId(), record)) {
            return record;
        }
        // 延迟发送时间，截止发送时间，链接过期时间
        LocalDateTime[] sendTimeAndDeadTime = new LocalDateTime[]{null, null};
        if (!checkDeadTime(sendManage.getSendMoment(), record, params, sendTimeAndDeadTime)) {
            return record;
        }
        LocalDateTime sendTime = sendTimeAndDeadTime[0];
        LocalDateTime deadTime = sendTimeAndDeadTime[1];
        if (sendTime != null) {
            record.setSendTime(Date.from(sendTime.atZone(ZoneId.systemDefault()).toInstant()));
        } else {
            record.setSendTime(new Date());
        }
        // 过期时间
        LocalDateTime expireTime = getExpireTime(sendManage.getExpireMoment(), sendTime);
        // 创建短链
        SurveyLinkDto link = buildSurveyUrl(record, surveyId, record.getClientId(), expireTime, customer, params);
        record.setLinkId(link.getLinkId());
        record.setSendUrl(link.getShortUrl());
        // 解析推送渠道数据，更新到推送记录
        TaskCustomerSendCompositedDto send = new TaskCustomerSendCompositedDto();
        if (!buildSendChannel(send, taskProgressId, sendTime, deadTime, sendManage, record, link, survey.getTitle(), customer, params)) {
            return record;
        }
        // 添加推送任务 CUSTOMER_SEND_COMPOSITED
        Duration delay = sendTime == null ? null : Duration.between(LocalDateTime.now(), sendTime);
        ctmTaskTrigger.customerSendComposited(orgId, userId, sendManage.getTriggerType().name(), sourceId, JsonHelper.toJson(send), delay);
        sendManageRecordRepository.save(record);
        return record;
    }

    private Long getRandomSurveyId(List<Long> sids) {
        if (sids == null || sids.isEmpty()) {
            return null;
        }
        int i = RandomUtils.nextInt(0, sids.size());
        return sids.get(i);
    }

    private SendManageRecord buildRecord(SendManage sendManage, Long sourceId, Long surveyId, Long customerId) {
        SendManageRecord record = new SendManageRecord();
        record.setOrgId(sendManage.getOrgId());
        record.setSendManageId(sendManage.getId());
        record.setSourceId(sourceId);
        record.setSurveyId(surveyId);
        record.setCustomerId(customerId);
        record.setClientId(UUID.randomUUID().toString());
        sendManageRecordRepository.save(record);
        return record;
    }

    private boolean disturb(DisturbMomentDto disturbMoment, Long customerId, SendManageRecord record) {
        if (customerId == null || disturbMoment == null || disturbMoment.getEnable() == null || !disturbMoment.getEnable()
                || disturbMoment.getType() == null
                || disturbMoment.getValue() == null || disturbMoment.getValue() <= 0) {
            return false;
        }
        boolean disturb = false;
        if (disturbMoment.getType() == DisturbType.TIME) {
            // start 到 now 存在发送记录，则此次为免打扰状态
            Date start = DateHelper.toDate(LocalDateTime.now().minusDays(disturbMoment.getValue()));
            long size = sendManageRecordRepository.countByIdNotAndSendManageIdAndCustomerIdAndStatusNotAndCreateTimeAfterAllIgnoreCase(
                    record.getId(), record.getSendManageId(), customerId,
                    SendManageRecordStatus.DISTURB, start);
            disturb = size > 0;
        } else if (disturbMoment.getType() == DisturbType.COUNT) {
            // 最近的 n 条记录有发送过，则此次为免打扰状态
            PageRequest pageRequest = PageRequest.of(0, disturbMoment.getValue());
            List<SendManageRecord> list = sendManageRecordRepository.findByIdNotAndSendManageIdAndCustomerIdOrderByIdDesc(record.getId(), record.getSendManageId(), customerId, pageRequest);
            if (CollectionUtils.isNotEmpty(list)) {
                disturb = list.stream().anyMatch(i -> i.getStatus() != SendManageRecordStatus.DISTURB);
            }
        }
        if (disturb) {
            record.setStatus(SendManageRecordStatus.DISTURB);
            sendManageRecordRepository.save(record);
        }
        return disturb;
    }

    private boolean checkDeadTime(PushMomentDto pushMoment, SendManageRecord record, Map<String, Object> params, LocalDateTime[] sendTimeAndDeadTime) {
        if (pushMoment == null || pushMoment.getMomentType() == null || pushMoment.getMomentType() == MomentType.IMMEDIATELY) {
            return true;
        }
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime baseTime = null;
        if (pushMoment.getEnableBaseTimeParam() != null && pushMoment.getEnableBaseTimeParam() && StringUtils.isNotEmpty(pushMoment.getBaseTimeParam())) {
            Object i = params.get(pushMoment.getBaseTimeParam());
            if (i instanceof Date) {
                baseTime = DateHelper.toLocalDateTime((Date) i);
            } else if (i instanceof String) {
                baseTime = DateHelper.toLocalDateTime(DateUtil.parse(i.toString()));
            }

        }
        if (baseTime == null) {
            baseTime = now;
        }
        Duration delay;
        if (pushMoment.getMomentType() == MomentType.LATER) {
            delay = TimeUtils.parseDuration(pushMoment.getDuration());
        } else {
            TimedTaskDto timed = TimedTaskDto.builder()
                    .timedType(pushMoment.getTimedType())
                    .dayOfWeeks(pushMoment.getWeeks())
                    .hour(pushMoment.getHour())
                    .minute(pushMoment.getMinute())
                    .build();
            delay = TimeUtils.parseAtDuration(timed);
        }
        Duration dead = null;
        if (pushMoment.getEnableDeadDuration() != null && pushMoment.getEnableDeadDuration()) {
            dead = TimeUtils.parseDuration(pushMoment.getDeadDuration());
        }
        LocalDateTime sendTime = null;
        LocalDateTime deadTime = null;
        if (delay != null) {
            sendTime = baseTime.plusSeconds(delay.getSeconds());
            if (sendTime.isBefore(now)) {
                sendTime = now;
            }
        }
        if (dead != null) {
            deadTime = baseTime.plusSeconds(dead.getSeconds());
        }
        sendTimeAndDeadTime[0] = sendTime;
        sendTimeAndDeadTime[1] = deadTime;
        if (deadTime != null && deadTime.isBefore(now)) {
            record.setStatus(SendManageRecordStatus.EXPIRE);
            sendManageRecordRepository.save(record);
            return false;
        }
        return true;
    }

    private LocalDateTime getExpireTime(ExpireMomentDto expireMoment, LocalDateTime sendTime) {
        if (expireMoment == null || expireMoment.getEnable() == null || !expireMoment.getEnable()) {
            return null;
        }
        return expireMoment.calcExpireTime(sendTime);
    }

    private SurveyLinkDto buildSurveyUrl(SendManageRecord record, Long surveyId, String clientId, LocalDateTime expireTime, Customer customer, Map<String, Object> params) {
        Map<String, Object> linkParams = new HashMap<>();
        linkParams.put("clientId", clientId);
        linkParams.put("trackId", "SEND_MANAGE:" + record.getId());
        if (customer.getId() != null) {
            linkParams.put("customerId", customer.getId());
        }
        if (StringUtils.isNotEmpty(customer.getExternalUserId())) {
            linkParams.put("externalUserId", customer.getExternalUserId());
        }
        if (expireTime != null) {
            linkParams.put("expireTime", DateHelper.format(expireTime, DateHelper.DATE_TIME_FORMATTER2));
            stringRedisTemplate.opsForHash().put(CustomerClientIdParamDto.clientIdParamKey(clientId), "expireTime", DateHelper.format(expireTime, DateHelper.DATE_TIME_FORMATTER));
        }
        Map<String, Object> parameters = new HashMap<>();

        if (MapUtils.isNotEmpty(params)) {
            List<String> systemParamKeys = List.of(
                    "sceneId",
                    "customerId",
                    "externalUserId",
                    "departmentCode",
                    "departmentId",
                    "externalCompanyId",
                    "channelId");
            params.forEach((k, v) -> {
                if (v == null) {
                    return;
                }
                if (k.equals("parameters")) {
                    if (v instanceof Map) {
                        parameters.putAll((Map<String, ?>) v);
                    }
                } else if (k.startsWith("_")) {
                    parameters.put(k.replaceFirst("_", ""), v);
                } else if (systemParamKeys.contains(k)) {
                    linkParams.put(k, v);
                } else {
                    parameters.put(k, v);
                }
            });
        }
        if (customer.getUrlCustomParams() != null && !customer.getUrlCustomParams().isEmpty()) {
            parameters.putAll(customer.getUrlCustomParams());
        }
        if (!parameters.isEmpty()) {
            linkParams.put("parameters", parameters);
        }
        Link link = linkService.createLink(surveyId, 2, linkParams);
        SurveyLinkDto dto = new SurveyLinkDto();
        dto.setLinkId(link.getId());
        dto.setOriginUrl(linkService.toOriginUrl(link));
        dto.setShortUrl(linkService.toShortUrl(link));
        dto.setShortCode(linkService.toShortCode(link));
        return dto;
    }

    public boolean buildSendChannel(TaskCustomerSendCompositedDto send, Long taskProgressId,
                                     LocalDateTime sendTime, LocalDateTime deadTime,
                                     SendManage sendManage, SendManageRecord record,
                                     SurveyLinkDto link, String surveyName, Customer customer, Map<String, Object> params) {
        send.setFromType(CustomerSendFromType.SEND_MANAGE);
        send.setFromId(record.getId());
        send.setDeadTime(DateHelper.formatDateTime(deadTime));
        send.setSmsPostPaid(true);
        if (taskProgressId != null) {
            send.setTaskProgressId(taskProgressId);
        }
        if (CollectionUtils.isNotEmpty(sendManage.getChannel())) {
            sendManage.getChannel().forEach(channel -> {
                try {
                    if (channel.getType() == SendManageChannelType.SMS) {
                        if (channelSmsSilent(sendManage.getSendMoment(), sendTime)) {
                            channelSms(link, surveyName, customer, params, channel, send);
                        }
                    } else if (channel.getType() == SendManageChannelType.EMAIL) {
                        channelEmail(link, surveyName, customer, params, channel, send);
                    } else if (channel.getType() == SendManageChannelType.WECHAT) {
                        channelWechat(link, surveyName, customer, params, channel, send);
                    } else if (channel.getType() == SendManageChannelType.APP) {
                        if (channelApiCondition(channel, params)) {
                            channelApi(link, surveyName, customer, params, channel, send);
                        }
                    }
                } catch (Throwable e) {
                    log.error("send manage channel error {}", e.getMessage());
                }
            });
            record.setSendComposited(send);
            record.setSendChannelInfo(JsonHelper.toJson(send));
        }
        if (send.getSms().isEmpty()
                && send.getWechat().isEmpty()
                && send.getEmail().isEmpty()
                && send.getApi().isEmpty()) {
            record.setStatus(SendManageRecordStatus.NO_CHANNEL);
            sendManageRecordRepository.save(record);
            return false;
        }
        return true;
    }

    private boolean channelApiCondition(SendChannelConfigDto c, Map<String, Object> params) {
        if (StringUtils.isNotEmpty(c.getApiCondition())) {
            return params.containsKey(c.getApiCondition());
        }
        return true;
    }

    private void channelApi(SurveyLinkDto link, String surveyName, Customer customer, Map<String, Object> params, SendChannelConfigDto c, TaskCustomerSendCompositedDto send) {
        Connector connector = connectorService.get(c.getApiConnectorId());
        if (connector != null) {
            Map<String, Object> body = new HashMap<>();
            body.put("surveyUrl", link.getShortUrl());
            body.put("surveyName", surveyName);
            body.put("customerId", customer.getId());
            body.put("externalUserId", customer.getExternalUserId());
            body.put("gatewayName", connector.getName());
            body.put("gatewayUrl", connector.getGateway());
            body.putAll(params);
            TaskCustomerSendCompositedDto.CompositedApi api = new TaskCustomerSendCompositedDto.CompositedApi();
            api.setPriority(c.getLevel().ordinal());
            api.setBody(JsonHelper.toJson(body));
            api.setConnectorId(connector.getId());
            send.getApi().add(api);
        }
    }

    private void channelWechat(SurveyLinkDto link, String surveyName, Customer customer, Map<String, Object> params, SendChannelConfigDto c, TaskCustomerSendCompositedDto send) {
        Long wechatOpenConfigId = null;
        String openId = null;
        // 优先级1 客户信息中指定了 openId
        if (StringUtils.isNotEmpty(customer.getWechatParams().getOpenId())) {
            WechatOpenConfig wechatOpenConfig;
            if (StringUtils.isNotEmpty(customer.getWechatParams().getAppId())) {
                wechatOpenConfig = customerService.getWechatOpenConfig(customer.getWechatParams().getAppId());
            } else {
                wechatOpenConfig = customerService.getWechatOpenSingleConfig();
            }
            if (wechatOpenConfig != null) {
                wechatOpenConfigId = wechatOpenConfig.getConfigId();
                openId = customer.getWechatParams().getOpenId();
            }
        }
        if (wechatOpenConfigId == null) {
            // 优先级2 客户关联的 ThirdPartyCustomer
            ThirdPartyCustomer thirdPartyCustomer = customerMessageService.getWechatCustomer(customer);
            if (thirdPartyCustomer != null) {
                WechatOpenConfig wechatOpenConfig = customerService.getWechatOpenConfig(thirdPartyCustomer.getThirdpartyAuthId());
                if (wechatOpenConfig != null) {
                    wechatOpenConfigId = wechatOpenConfig.getConfigId();
                    openId = thirdPartyCustomer.getOpenId();
                }
            }
        }
        if (wechatOpenConfigId != null && StringUtils.isNotEmpty(openId)) {
            Map<String, Object> templateContent = c.getContent();
            TemplateInfoDto templateInfo = null;
            if (c.getWechatOpenTemplateId() != null) {
                templateInfo = customerMessageService.getWeChatTemplate(c.getWechatOpenTemplateId(), wechatOpenConfigId, templateContent);
            } else if (c.getThirdpartyTemplateId() != null) {
                templateInfo = customerMessageService.getWeChatTemplate(c.getThirdpartyTemplateId(), templateContent);
            }
            if (templateInfo != null) {
                Map<String, Object> parameters = customerMessageService.buildNativeParams(link, surveyName, customer, params);
                String content = customerMessageService.buildWechatContent(templateInfo.getWeChatTemplateId(), openId, link.getOriginUrl(), templateInfo.getWeChatContent(), parameters);
                TaskCustomerSendCompositedDto.CompositedWechat wechat = new TaskCustomerSendCompositedDto.CompositedWechat();
                wechat.setPriority(c.getLevel().ordinal());
                wechat.setAppId(templateInfo.getWeChatAppId());
                wechat.setOpenId(openId);
                wechat.setTemplateId(templateInfo.getWeChatTemplateId());
                wechat.setThirdpartyAuthId(wechatOpenConfigId);
                wechat.setMessage(content);
                wechat.setUrl(link.getOriginUrl());
                send.getWechat().add(wechat);
            }
        }
    }

    private void channelEmail(SurveyLinkDto link, String surveyName, Customer customer, Map<String, Object> params, SendChannelConfigDto c, TaskCustomerSendCompositedDto send) {
        String account = (String) params.get("email");
        if (StringUtils.isEmpty(account)) {
            account = customer.getEmail();
        }
        if (StringUtils.isEmpty(account)) {
            return;
        }
        String sender = (String) c.getContent().get("sender");
        String replaceTitle = (String) c.getContent().get("title");
        String replaceText = (String) c.getContent().get("content");
        TemplateInfoDto templateInfo = customerMessageService.getEmailTemplate(c.getThirdpartyTemplateId(), sender, replaceTitle, replaceText);
        Map<String, Object> data = new HashMap<>();
        data.put("title", templateInfo.getEmailTitle());
        data.put("content", templateInfo.getEmailContent());
        Map<String, Object> parameters = customerMessageService.buildNativeParams(link, surveyName, customer, params);
        data = customerMessageService.buildEmailContent(data, parameters);
        String subject = (String) data.get("title");
        String content = (String) data.get("content");
        TaskCustomerSendCompositedDto.CompositedEmail email = new TaskCustomerSendCompositedDto.CompositedEmail();
        email.setPriority(c.getLevel().ordinal());
        email.setContent(content);
        email.setEmail(account);
        email.setSender(sender);
        email.setTitle(subject);
        send.getEmail().add(email);
    }

    private boolean channelSmsSilent(PushMomentDto pushMoment, LocalDateTime sendTime) {
        // 必须设置了推送条件
        if (pushMoment != null && pushMoment.getEnableSilentTime() != null && pushMoment.getEnableSilentTime() && StringUtils.isNotEmpty(pushMoment.getSilentTime())) {
            sendTime = sendTime == null ? LocalDateTime.now() : sendTime;
            LocalDate sendDate = sendTime.toLocalDate();
            // 解析静默时间段
            String[] as = pushMoment.getSilentTime().split("-");
            if (as.length == 2) {
                LocalDateTime silentStartTime = null, silentEndTime = null;
                // 静默开始时间
                String[] start = as[0].split(":");
                if (start.length == 2 && NumberUtils.isDigits(start[0]) && NumberUtils.isDigits(start[1])) {
                    silentStartTime = sendDate.atTime(Integer.parseInt(start[0]), Integer.parseInt(start[1]));
                }
                // 静默结束时间
                String[] end = as[1].split(":");
                if (end.length == 2 && NumberUtils.isDigits(end[0]) && NumberUtils.isDigits(end[1])) {
                    silentEndTime = sendDate.atTime(Integer.parseInt(end[0]), Integer.parseInt(end[1]));
                }
                if (silentStartTime != null && silentEndTime != null) {
                    // 如果静默结束时间大于开始时间，结束时间需要+1天
                    if (silentEndTime.isBefore(silentStartTime)) {
                        silentEndTime = silentEndTime.plusDays(1);
                    }
                    // 发送时间在静默时间段内，则不发送
                    if (sendTime.isAfter(silentStartTime) && sendTime.isBefore(silentEndTime)) {
                        return false;
                    }
                }
            }
        }
        return true;
    }

    private void channelSms(SurveyLinkDto link, String surveyName, Customer customer, Map<String, Object> params, SendChannelConfigDto c, TaskCustomerSendCompositedDto send) {
        String mobile = (String) params.get("mobile");
        if (StringUtils.isEmpty(mobile)) {
            mobile = customer.getMobile();
        }
        if (StringUtils.isEmpty(mobile)) {
            return;
        }
        TemplateInfoDto templateInfo = customerMessageService.getSmsTemplate(c.getThirdpartyTemplateId(), (String) c.getContent().get("content"));
        Map<String, Object> parameters = customerMessageService.buildNativeParams(link, surveyName, customer, params);
        c.getContent().forEach((k, v) -> {
            if (v != null && !v.toString().isEmpty() && !"content".equals(k)) {
                String value = TemplateEngine.renderTextTemplate(v.toString(), parameters);
                parameters.put(k, value);
            }
        });
        String content = customerMessageService.buildSmsContent(templateInfo.getSmsContent(), parameters);
        TaskCustomerSendCompositedDto.CompositedSms sms = new TaskCustomerSendCompositedDto.CompositedSms();
        sms.setPriority(c.getLevel().ordinal());
        sms.setContent(content);
        sms.setMobile(mobile);
        sms.setTemplateId(templateInfo.getSmsTemplateId());
        sms.setTemplateName(templateInfo.getSmsTemplateName());
        sms.setTemplateContent(templateInfo.getSmsContent());
        sms.setSignId(templateInfo.getSmsSignId());
        sms.setRealSign(templateInfo.getSmsRealSign());
        send.getSms().add(sms);
    }
}
