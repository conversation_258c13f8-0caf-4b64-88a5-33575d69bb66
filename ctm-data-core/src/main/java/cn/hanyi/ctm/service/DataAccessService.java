package cn.hanyi.ctm.service;

import cn.hanyi.ctm.dto.DataAccessSimpleDto;
import cn.hanyi.ctm.entity.DataAccess;
import cn.hanyi.ctm.entity.DataAccessDto;
import cn.hanyi.ctm.entity.DataAccessParams;
import cn.hanyi.ctm.entity.DataAccessParamsDto;
import cn.hanyi.ctm.repository.DataAccessCellRepository;
import cn.hanyi.ctm.repository.DataAccessRepository;
import cn.hanyi.ctm.workertrigger.ICtmEventTrigger;
import lombok.extern.slf4j.Slf4j;
import org.befun.core.dto.BaseEntityDTO;
import org.befun.core.dto.ResourceBatchUpdateRequestDto;
import org.befun.core.dto.ResourceUpdateItemRequestDto;
import org.befun.core.dto.query.ResourceEntityQueryDto;
import org.befun.core.entity.BaseEntity;
import org.befun.core.rest.context.TenantData;
import org.befun.core.service.BaseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

@Service
@Slf4j
public class DataAccessService extends BaseService<DataAccess, DataAccessDto, DataAccessRepository> {

    @Autowired
    private ICtmEventTrigger eventTrigger;

    @Autowired
    private DataAccessCellRepository cellRepository;

    @Autowired
    private DataAccessParamService paramService;
    @Autowired
    private DataAccessRepository dataAccessRepository;


    @Override
    public void afterMapToDto(List<DataAccess> entity, List<DataAccessDto> dto) {
        dto.forEach(d -> d.setCellNumber(cellRepository.countByDataAccess(d.getEntity())));
        super.afterMapToDto(entity, dto);
    }

    @Override
    public Page<DataAccessDto> findAll(ResourceEntityQueryDto<DataAccessDto> queryDto) {
        return super.findAll(queryDto);
    }

    public Page<DataAccessSimpleDto> findAllSimple(ResourceEntityQueryDto<DataAccessSimpleDto> queryDto) {
        queryDto.setPage(queryDto.getPage() - 1);
        return dataAccessRepository.findSimpleByOrgId(TenantData.current().getOrgId(),
                PageRequest.of(queryDto.getPage(), queryDto.getLimit(), queryDto.getSorts()));
    }

    @Transactional
    public <EE extends BaseEntity, ED extends BaseEntityDTO<EE>> List<ED> batchUpdateEmbeddedMany(Long id, String fieldNameInRoot, Class<EE> eeClass, Class<ED> edClass, ResourceBatchUpdateRequestDto<ED> batchChangeDto) {
        List<ED> addData = new ArrayList<>();
        List<ResourceUpdateItemRequestDto<ED>> changes = batchChangeDto.getChanges();
        AtomicInteger sequence = new AtomicInteger(changes.size());
        changes.stream()
                .filter(d -> d.getData() != null)
                .filter(d -> d.getId() == null)
                .forEach(d -> {
                    DataAccessParamsDto data = (DataAccessParamsDto) d.getData();
                    data.setSequence(sequence.getAndIncrement());
                    addData.add(createEmbeddedMany(id, fieldNameInRoot, eeClass, edClass, (ED) data));
                });


        batchChangeDto.getChanges().stream().filter(i -> i.getData() != null && i.getId() != null).forEach(p -> {
            DataAccessParams params = paramService.require(p.getId());
            ED data = p.getData();
            params.setParamsValue(Collections.emptyMap());
            data.setEntity((EE) params);
            params.setParamsValue(((DataAccessParamsDto) data).getParamsValue());
            paramService.save(params);
        });

        batchChangeDto.getChanges().removeIf(i -> i.getData() == null || i.getId() == null);

        List<ED> updateDta = super.batchUpdateEmbeddedMany(fieldNameInRoot, eeClass, edClass, batchChangeDto);
        // 合并add和update的结果
        updateDta.addAll(addData);
        // 根据sequence排序
        updateDta.sort((o1, o2) -> {
            DataAccessParamsDto d1 = (DataAccessParamsDto) o1;
            DataAccessParamsDto d2 = (DataAccessParamsDto) o2;
            return d1.getSequence() - d2.getSequence();
        });
        DataAccess dataAccess = require(id);
        dataAccess.setVersion(dataAccess.getVersion() + 1);
        return updateDta;
    }

    @Override
    public <S extends BaseEntityDTO<DataAccess>> DataAccessDto updateOne(long id, S change) {
        DataAccess dataAccess = require(id);
        ((DataAccessDto) change).setVersion(dataAccess.getVersion() + 1);
        return super.updateOne(id, change);
    }
}

