package cn.hanyi.ctm.entity;

import cn.hanyi.ctm.constant.ExpireMomentDto;
import cn.hanyi.ctm.constant.SendManageRepeatType;
import cn.hanyi.ctm.constant.SendManageTriggerType;
import cn.hanyi.ctm.constant.SendType;
import cn.hanyi.ctm.dto.SendChannelConfigDto;
import cn.hanyi.ctm.dto.TriggerEventType;
import cn.hanyi.ctm.dto.TriggerTimerDto;
import cn.hanyi.ctm.dto.customer.CustomerSelectedDto;
import cn.hanyi.ctm.dto.ext.SendManageChannelDto;
import cn.hanyi.ctm.dto.journey.DisturbMomentDto;
import cn.hanyi.ctm.dto.journey.DisturbMomentDtoConverter;
import cn.hanyi.ctm.dto.journey.PushMomentDto;
import cn.hanyi.ctm.dto.journey.PushMomentDtoConverter;
import cn.hanyi.survey.core.constant.channel.ChannelType;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.auth.projection.SimpleUser;
import org.befun.core.converter.LongRawListConverter;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.dto.annotation.DtoProperty;
import org.befun.core.entity.EnterpriseEntity;
import org.befun.core.entity.annotation.EntityScopeStrategy;
import org.befun.core.hibernate.types.JsonColumn;
import org.befun.core.rest.context.TenantContext;
import org.befun.core.rest.view.ResourceViews;
import org.hibernate.annotations.Type;

import javax.persistence.*;
import java.math.BigInteger;
import java.security.SecureRandom;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;

import static org.befun.core.constant.EntityScopeStrategyType.OWNER_GROUP_CORPORATION;

@Entity
@Table(name = "send_manage")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@EntityScopeStrategy(value = OWNER_GROUP_CORPORATION, resource = "SEND", groupResource = "SEND_GROUP")
@DtoClass(includeAllFields = true, superClass = SendManageChannelDto.class)
public class SendManage extends EnterpriseEntity {

    @Schema(description = "分组id")
    @Column(name = "group_id")
    private Long groupId;

    @Column(name = "title")
    @DtoProperty(queryable = true, jsonView = ResourceViews.Basic.class)
    private String title;

    @JsonView(ResourceViews.Basic.class)
    @Column(name = "enable")
    private Boolean enable = true;

    @JsonView(ResourceViews.Detail.class)
    @Column(name = "trigger_type")
    @Enumerated(EnumType.STRING)
    private SendManageTriggerType triggerType = SendManageTriggerType.JOURNEY;

    @JsonView(ResourceViews.Detail.class)
    @Column(name = "embed_type")
    @Enumerated(EnumType.STRING)
    private ChannelType embedType;

    @JsonView(ResourceViews.Detail.class)
    @Column(name = "trigger_id")
    private Long triggerId;

    @Type(type = JsonColumn.TYPE)
    @JsonView(ResourceViews.Detail.class)
    @Column(name = "trigger_timer")
    private TriggerTimerDto triggerTimer;

    @Type(type = JsonColumn.TYPE)
    @JsonView(ResourceViews.Detail.class)
    @Column(name = "trigger_timer_target")
    private CustomerSelectedDto triggerTimerTarget;

    @JsonView(ResourceViews.Detail.class)
    @Column(name = "last_trigger_timer")
    private Date lastTriggerTimer;

    @JsonView(ResourceViews.Detail.class)
    @Column(name = "send_token")
    private String sendToken = new BigInteger(80, new SecureRandom()).toString(32);

    @JsonView(ResourceViews.Basic.class)
    @Convert(converter = LongRawListConverter.class)
    @Column(name = "send_sids")
    private List<Long> sendSids = List.of();

    @JsonView(ResourceViews.Basic.class)
    @Convert(converter = LongRawListConverter.class)
    @Column(name = "send_cids")
    private List<Long> sendCids = List.of();

    @DtoProperty(queryable = true, jsonView = ResourceViews.Basic.class)
    @Column(name = "survey_titles")
    private String surveyTitles;

    @JsonView(ResourceViews.Detail.class)
    @Column(name = "send_filter")
    private String sendFilter;

    @JsonView(ResourceViews.Detail.class)
    @Column(name = "qr_code")
    private String qrCode;

    @JsonView(ResourceViews.Basic.class)
    @Convert(converter = PushMomentDtoConverter.class)
    @Column(name = "send_moment")
    private PushMomentDto sendMoment;

    @JsonView(ResourceViews.Basic.class)
    @Column(name = "send_type")
    @Enumerated(EnumType.STRING)
    private SendType sendType = SendType.MESSAGE;

    @JsonView(ResourceViews.Detail.class)
    @Column(name = "channel")
    @Type(type = JsonColumn.TYPE)
    private List<SendChannelConfigDto> channel;

    @JsonView(ResourceViews.Detail.class)
    @Column(name = "disturb_moment")
    @Schema(description = "勿扰模式")
    @Convert(converter = DisturbMomentDtoConverter.class)
    private DisturbMomentDto disturbMoment = new DisturbMomentDto();

    @JsonView(ResourceViews.Detail.class)
    @Column(name = "expire_moment")
    @Schema(description = "链接有效期")
    @Type(type = JsonColumn.TYPE)
    private ExpireMomentDto expireMoment = new ExpireMomentDto();

    @JsonView(ResourceViews.Basic.class)
    @Column(name = "repeat_times")
    @Enumerated(EnumType.STRING)
    private SendManageRepeatType repeatTimes = SendManageRepeatType.SINGLE;

    @Column(name = "user_id")
    private Long userId;

    @Column(name = "editor_id")
    private Long editorId;

    @DtoProperty(jsonView = ResourceViews.Basic.class)
    @Transient
    private SimpleUser creator;

    @DtoProperty(jsonView = ResourceViews.Basic.class)
    @Transient
    private SimpleUser editor;

    @DtoProperty(jsonView = ResourceViews.Detail.class)
    @Transient
    private List<TriggerEventType> triggerEventTypes = new ArrayList<>();

    @PreUpdate
    public void preUpdate() {
        Optional.ofNullable(TenantContext.getCurrentUserId()).ifPresent(i -> editorId = i);
    }

    @PrePersist
    public void prePersist() {
        userId = userId == null ? TenantContext.getCurrentUserId() : userId;
        editorId = userId;
    }

}

