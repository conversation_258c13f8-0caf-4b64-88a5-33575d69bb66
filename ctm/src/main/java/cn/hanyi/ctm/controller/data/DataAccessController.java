package cn.hanyi.ctm.controller.data;

import cn.hanyi.ctm.dataaccess.datahub.mock.DataHubMockConnection;
import cn.hanyi.ctm.dto.DataAccessSimpleDto;
import cn.hanyi.ctm.entity.DataAccess;
import cn.hanyi.ctm.entity.DataAccessCellDto;
import cn.hanyi.ctm.entity.DataAccessParams;
import cn.hanyi.ctm.entity.DataAccessParamsDto;
import cn.hanyi.ctm.properties.DataHubProperties;
import cn.hanyi.ctm.repository.DataAccessParamsRepository;
import cn.hanyi.ctm.repository.DataAccessRepository;
import cn.hanyi.ctm.service.DataAccessCellService;
import cn.hanyi.ctm.service.DataAccessService;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.befun.core.dto.ResourceBatchUpdateRequestDto;
import org.befun.core.dto.ResourceListResponseDto;
import org.befun.core.dto.ResourcePageResponseDto;
import org.befun.core.dto.ResourceResponseDto;
import org.befun.core.dto.query.ResourceEntityQueryDto;
import org.befun.core.dto.query.ResourceQueryCriteria;
import org.befun.core.rest.BaseController;
import org.befun.core.rest.annotation.ResourceController;
import org.befun.core.rest.annotation.ResourceEmbeddedMany;
import org.befun.core.rest.annotation.ResourceQueryPredicate;
import org.befun.core.rest.view.ResourceViews;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

import static org.befun.core.rest.annotation.processor.ResourceMethod.BATCH_UPDATE;

@Slf4j
@Tag(name = "数据接入")
@PreAuthorize("isAuthenticated()")
@RestController
@RequestMapping("/data-access")
@ResourceController(
        entityClass = DataAccess.class,
        repositoryClass = DataAccessRepository.class,
        serviceClass = DataAccessService.class,
        permission = "isAuthenticated()",
        docTag = "数据接入",
        docCrud = "数据接入"
)
@ResourceEmbeddedMany(
        path = "params",
        fieldNameInRoot = "paramsConfiguration",
        entityClass = DataAccessParams.class,
        repositoryClass = DataAccessParamsRepository.class,
        excludeActions = {BATCH_UPDATE},
        docTag = "数据接入-参数",
        docCrud = "数据接入-参数"
)
public class DataAccessController extends BaseController<DataAccessService> {

    @Autowired
    private StringRedisTemplate redisTemplate;
    @Autowired
    private DataHubProperties dataHubProperties;

    @Autowired
    private DataAccessCellService cellService;

    @PostMapping("add-datahub-mock-data/{dataAccessId}")
    @Operation(summary = "添加datahub模拟数据")
    public ResourceResponseDto<Boolean> addDataHubMockData(
            @PathVariable Long dataAccessId,
            @RequestBody List<Map<String, Object>> data) {
        DataHubMockConnection.addMockData(redisTemplate, dataAccessId, dataHubProperties.getMockConnectionQueue(), data);
        return new ResourceResponseDto<>(true);
    }

    @PostMapping("push-datahub-mock-data/{dataAccessId}")
    @Operation(summary = "添加datahub模拟数据")
    public ResourceResponseDto<Boolean> pushDataHubMockData(
            @PathVariable Long dataAccessId,
            @RequestBody List<Map<String, Object>> data) {
        DataHubMockConnection.pushMockData(redisTemplate, dataAccessId, dataHubProperties.getMockConnectionQueue(), data);
        return new ResourceResponseDto<>(true);
    }


    @PostMapping("/{id}/params/batch")
    @Operation(
            summary = "批量修改数据接入-参数"
    )
    @Tag(
            name = "数据接入-参数"
    )
    @JsonView(ResourceViews.Basic.class)
    public ResourceListResponseDto<DataAccessParamsDto> batchUpdateDataAccessParams(
            @PathVariable long id,
            @RequestBody ResourceBatchUpdateRequestDto<DataAccessParamsDto> changes) {
        return new ResourceListResponseDto(service.batchUpdateEmbeddedMany(id, "paramsConfiguration", cn.hanyi.ctm.entity.DataAccessParams.class, cn.hanyi.ctm.entity.DataAccessParamsDto.class, changes));
    }

    @GetMapping("{id}/cell")
    @Operation(
            summary = "全部数据接入详情"
    )
    @Tag(
            name = "数据接入详情"
    )
    @JsonView(ResourceViews.Basic.class)
    public ResourcePageResponseDto<DataAccessCellDto> findCellAll(
            @PathVariable Long id,
            @ResourceQueryPredicate @Valid ResourceEntityQueryDto<DataAccessCellDto> params) {
        DataAccess dataAccess = service.require(id);
        params.getQueryCriteriaList().add(new ResourceQueryCriteria("dataAccess", dataAccess));
        return new ResourcePageResponseDto(cellService.findAll(params));
    }

    @GetMapping("/simple")
    @Operation(
            summary = "全部数据接入"
    )
    @Tag(
            name = "数据接入"
    )
    @JsonView(ResourceViews.Basic.class)
    public ResourcePageResponseDto<DataAccessSimpleDto> findAll(
            @ResourceQueryPredicate @Valid ResourceEntityQueryDto<DataAccessSimpleDto> params) {
        return new ResourcePageResponseDto(service.findAllSimple(params));
    }
}